<#
.SYNOPSIS
Messenger Application Uninstall - Datto RMM Applications Component

.DESCRIPTION
Intelligently detects and uninstalls Messenger application from both system and user contexts:
- Automatic detection of installation context (system vs user)
- Registry-based software detection (avoids Win32_Product)
- Handles both MSI and user-level installations
- Process termination before uninstall
- Comprehensive logging and error handling
- Supports multiple Messenger variants (Messenger, Chatgenie Messenger)

.COMPONENT
Category: Applications (Software Removal)
Execution: On-demand or scheduled
Timeout: Up to 15 minutes
Changeable: Yes (can be changed to Scripts category if needed)

.ENVIRONMENT VARIABLES
- ForceKill (Boolean): Force kill processes even if they don't respond to graceful termination (default: true)
- SkipUserContext (Boolean): Skip user context uninstall and only check system context (default: false)
- SkipSystemContext (Boolean): Skip system context uninstall and only check user context (default: false)
- VerifyUninstall (Boolean): Verify uninstall completed successfully after execution (default: true)
- RetryAttempts (Integer): Number of retry attempts for failed uninstalls (default: 2)
- UseFallbackMethods (Boolean): Use aggressive fallback methods when standard uninstall fails (default: true)
- DetailedLogging (Boolean): Enable detailed command output and debugging information (default: true)

.PARAMETER None
This script does not accept parameters. All configuration is handled via environment variables.

.INPUTS
None. This script does not accept pipeline input.

.OUTPUTS
System.String - Progress messages and status information

.EXAMPLE
# Datto RMM Applications Component Usage:
# Environment Variables:
# ForceKill = true
# SkipUserContext = false
# SkipSystemContext = false
# VerifyUninstall = true
# Component Type: Applications
# Timeout: 15 minutes

.NOTES
Version: 1.1.0
Author: Datto RMM Function Library
Component Category: Applications (Software Removal)
Compatible: PowerShell 3.0+, Datto RMM Environment

Datto RMM Applications Exit Codes:
- 0: Success (uninstall completed or software not found)
- 1: Failed (uninstall failed or error occurred)
- 2: Partial success (some installations removed but others failed)

CHANGELOG:
1.1.0 - Enhanced resilience: fixed string interpolation, added retry logic, improved error detection, fallback methods
1.0.0 - Initial Messenger uninstall script with dual context support
#>

# Embedded environment variable function (pattern from shared-functions/Core/RMMValidation.ps1)
function Get-RMMVariable {
    param(
        [string]$Name,
        [string]$Type = "String",
        $Default = $null
    )

    $envValue = [Environment]::GetEnvironmentVariable($Name)
    if ([string]::IsNullOrWhiteSpace($envValue)) { return $Default }

    switch ($Type) {
        "Integer" {
            try { [int]$envValue }
            catch { $Default }
        }
        "Boolean" {
            $envValue -eq 'true' -or $envValue -eq '1' -or $envValue -eq 'yes' -or $envValue -eq 'on'
        }
        default { $envValue }
    }
}

# Embedded logging function (pattern from shared-functions/Core/RMMLogging.ps1)
function Write-RMMLog {
    param(
        [Parameter(Mandatory=$true)]
        [AllowEmptyString()]
        [string]$Message,

        [ValidateSet("Info", "Status", "Success", "Warning", "Error", "Config", "Detect")]
        [string]$Level = "Info"
    )

    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'

    # Handle empty messages for spacing
    if ([string]::IsNullOrEmpty($Message)) {
        $logMessage = ""
    } else {
        $logMessage = "[$timestamp] [$Level] $Message"
    }

    if ([string]::IsNullOrEmpty($logMessage)) {
        Write-Host ""  # Empty line for spacing
    } else {
        switch ($Level) {
            "Success" { Write-Host $logMessage -ForegroundColor Green }
            "Warning" { Write-Host $logMessage -ForegroundColor Yellow }
            "Error" { Write-Host $logMessage -ForegroundColor Red }
            "Status" { Write-Host $logMessage -ForegroundColor Cyan }
            "Config" { Write-Host $logMessage -ForegroundColor Magenta }
            "Detect" { Write-Host $logMessage -ForegroundColor Blue }
            default { Write-Host $logMessage }
        }
    }
}

# Configuration
$LogPath = "C:\ProgramData\DattoRMM\Applications"
if (-not (Test-Path $LogPath)) {
    New-Item -Path $LogPath -ItemType Directory -Force | Out-Null
}

# Start transcript (embedded pattern from shared-functions/Core/RMMLogging.ps1)
Start-Transcript -Path "$LogPath\MessengerUninstall-Applications.log" -Append

Write-RMMLog "=============================================="
Write-RMMLog "Messenger Application Uninstall - Applications Component v1.1.0" -Level Status
Write-RMMLog "=============================================="
Write-RMMLog "Component Category: Applications (Software Removal)" -Level Config
Write-RMMLog "Start Time: $(Get-Date)" -Level Config
Write-RMMLog "Log Directory: $LogPath" -Level Config
Write-RMMLog ""

# Process environment variables
$ForceKill = Get-RMMVariable -Name "ForceKill" -Type "Boolean" -Default $true
$SkipUserContext = Get-RMMVariable -Name "SkipUserContext" -Type "Boolean" -Default $false
$SkipSystemContext = Get-RMMVariable -Name "SkipSystemContext" -Type "Boolean" -Default $false
$VerifyUninstall = Get-RMMVariable -Name "VerifyUninstall" -Type "Boolean" -Default $true
$RetryAttempts = Get-RMMVariable -Name "RetryAttempts" -Type "Integer" -Default 2
$UseFallbackMethods = Get-RMMVariable -Name "UseFallbackMethods" -Type "Boolean" -Default $true
$DetailedLogging = Get-RMMVariable -Name "DetailedLogging" -Type "Boolean" -Default $true

# Validate RetryAttempts
if ($RetryAttempts -lt 0 -or $RetryAttempts -gt 5) {
    Write-RMMLog "Invalid RetryAttempts '$RetryAttempts'. Using default '2'" -Level Warning
    $RetryAttempts = 2
}

Write-RMMLog "Environment Variables:" -Level Config
Write-RMMLog "- ForceKill: $ForceKill" -Level Config
Write-RMMLog "- SkipUserContext: $SkipUserContext" -Level Config
Write-RMMLog "- SkipSystemContext: $SkipSystemContext" -Level Config
Write-RMMLog "- VerifyUninstall: $VerifyUninstall" -Level Config
Write-RMMLog "- RetryAttempts: $RetryAttempts" -Level Config
Write-RMMLog "- UseFallbackMethods: $UseFallbackMethods" -Level Config
Write-RMMLog "- DetailedLogging: $DetailedLogging" -Level Config
Write-RMMLog ""

function Test-UninstallPrerequisites {
    <#
    .SYNOPSIS
    Validates uninstall prerequisites and product codes before attempting removal
    #>
    param(
        [Parameter(Mandatory=$true)]
        [object]$Installation,
        [string]$Context = "Unknown"
    )

    $ValidationResults = [PSCustomObject]@{
        IsValid = $true
        Issues = @()
        ProductCode = $null
        UninstallString = $null
    }

    if ($Context -eq "System") {
        # Validate MSI product code
        if ([string]::IsNullOrWhiteSpace($Installation.ProductCode)) {
            $ValidationResults.IsValid = $false
            $ValidationResults.Issues += "Missing product code"
        } elseif ($Installation.ProductCode -notmatch '^{[A-F0-9]{8}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{12}}$') {
            $ValidationResults.IsValid = $false
            $ValidationResults.Issues += "Invalid product code format: $($Installation.ProductCode)"
        } else {
            $ValidationResults.ProductCode = $Installation.ProductCode

            # Verify product code exists in registry
            $RegPaths = @(
                "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\$($Installation.ProductCode)",
                "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\$($Installation.ProductCode)"
            )

            $ProductFound = $false
            foreach ($RegPath in $RegPaths) {
                if (Test-Path $RegPath) {
                    $ProductFound = $true
                    break
                }
            }

            if (-not $ProductFound) {
                $ValidationResults.IsValid = $false
                $ValidationResults.Issues += "Product code not found in registry: $($Installation.ProductCode)"
            }
        }
    } elseif ($Context -eq "User") {
        # Validate user uninstall string
        if ([string]::IsNullOrWhiteSpace($Installation.UninstallString)) {
            $ValidationResults.IsValid = $false
            $ValidationResults.Issues += "Missing uninstall string"
        } else {
            $ValidationResults.UninstallString = $Installation.UninstallString

            # Check if uninstaller executable exists
            if ($Installation.UninstallString -like "*Uninstall Messenger.exe*") {
                $UninstallPath = $Installation.UninstallString -replace '"', '' -replace ' /S', ''
                if (-not (Test-Path $UninstallPath)) {
                    $ValidationResults.IsValid = $false
                    $ValidationResults.Issues += "Uninstaller executable not found: $UninstallPath"
                }
            }
        }
    }

    if ($DetailedLogging -and $ValidationResults.Issues.Count -gt 0) {
        Write-RMMLog "Validation issues for $($Installation.DisplayName):" -Level Warning
        foreach ($Issue in $ValidationResults.Issues) {
            Write-RMMLog "  - $Issue" -Level Warning
        }
    }

    return $ValidationResults
}

function Stop-MessengerProcesses {
    <#
    .SYNOPSIS
    Terminates all Messenger-related processes
    #>
    param([bool]$ForceKill = $true)
    
    Write-RMMLog "Terminating Messenger processes..." -Level Status
    
    $ProcessNames = @("Messenger", "ChatgenieMessenger", "Messenger.exe", "ChatgenieMessenger.exe")
    $ProcessesKilled = 0
    
    foreach ($ProcessName in $ProcessNames) {
        $CleanName = $ProcessName -replace '\.exe$', ''
        $processes = Get-Process -Name $CleanName -ErrorAction SilentlyContinue
        
        if ($processes) {
            Write-RMMLog "Found $($processes.Count) instance(s) of $ProcessName" -Level Detect
            
            foreach ($process in $processes) {
                try {
                    if ($ForceKill) {
                        $process | Stop-Process -Force -ErrorAction Stop
                        Write-RMMLog "Force killed process: $ProcessName (PID: $($process.Id))" -Level Success
                    } else {
                        $process | Stop-Process -ErrorAction Stop
                        Write-RMMLog "Gracefully stopped process: $ProcessName (PID: $($process.Id))" -Level Success
                    }
                    $ProcessesKilled++
                } catch {
                    Write-RMMLog "Failed to stop process $ProcessName (PID: $($process.Id)): $($_.Exception.Message)" -Level Warning
                }
            }
        }
    }
    
    if ($ProcessesKilled -eq 0) {
        Write-RMMLog "No Messenger processes found running" -Level Detect
    } else {
        Write-RMMLog "Terminated $ProcessesKilled Messenger process(es)" -Level Success
        Start-Sleep -Seconds 2  # Allow processes to fully terminate
    }
}

function Get-SystemContextMessenger {
    <#
    .SYNOPSIS
    Detects Messenger installed in system context (MSI installations)
    #>
    
    Write-RMMLog "Checking for system context Messenger installations..." -Level Status
    
    $RegPaths = @(
        "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
        "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
    )
    
    $FoundInstallations = @()
    
    foreach ($RegPath in $RegPaths) {
        try {
            Get-ChildItem $RegPath -ErrorAction SilentlyContinue | ForEach-Object {
                $app = Get-ItemProperty $_.PSPath -ErrorAction SilentlyContinue
                if ($app.DisplayName -like "*Messenger*" -or $app.DisplayName -like "*Chatgenie*") {
                    $installation = [PSCustomObject]@{
                        DisplayName = $app.DisplayName
                        Publisher = $app.Publisher
                        Version = $app.DisplayVersion
                        ProductCode = $_.PSChildName
                        UninstallString = $app.UninstallString
                        InstallLocation = $app.InstallLocation
                        RegistryPath = $RegPath
                    }
                    $FoundInstallations += $installation
                    
                    Write-RMMLog "Found system installation: $($app.DisplayName)" -Level Detect
                    Write-RMMLog "  Publisher: $($app.Publisher)" -Level Detect
                    Write-RMMLog "  Version: $($app.DisplayVersion)" -Level Detect
                    Write-RMMLog "  Product Code: $($_.PSChildName)" -Level Detect
                }
            }
        } catch {
            Write-RMMLog "Error checking registry path $RegPath`: $($_.Exception.Message)" -Level Warning
        }
    }
    
    if ($FoundInstallations.Count -eq 0) {
        Write-RMMLog "No system context Messenger installations found" -Level Detect
    }
    
    return $FoundInstallations
}

function Get-UserContextMessenger {
    <#
    .SYNOPSIS
    Detects Messenger installed in user context across all user profiles
    #>

    Write-RMMLog "Checking for user context Messenger installations..." -Level Status

    $FoundInstallations = @()

    try {
        # Search user-level software (pattern from shared-functions/Core/RMMSoftwareDetection.ps1)
        Get-ChildItem "Registry::HKEY_USERS\" -ErrorAction SilentlyContinue | Where-Object { $_.PSIsContainer } | ForEach-Object {
            foreach ($node in @("Software", "Software\WOW6432Node")) {
                $uninstallPath = "Registry::$_\$node\Microsoft\Windows\CurrentVersion\Uninstall"

                if (Test-Path $uninstallPath -ErrorAction SilentlyContinue) {
                    try {
                        # Get user context information
                        $domainName = (Get-ItemProperty "Registry::$_\Volatile Environment" -Name USERDOMAIN -ErrorAction SilentlyContinue).USERDOMAIN
                        $username = (Get-ItemProperty "Registry::$_\Volatile Environment" -Name USERNAME -ErrorAction SilentlyContinue).USERNAME
                        $userContext = if ($username) { "$domainName\$username" } else { "Unknown User" }

                        Get-ChildItem $uninstallPath -ErrorAction SilentlyContinue | ForEach-Object {
                            $app = Get-ItemProperty $_.PSPath -ErrorAction SilentlyContinue
                            if ($app.DisplayName -like "*Messenger*" -or $app.DisplayName -like "*Chatgenie*") {
                                $installation = [PSCustomObject]@{
                                    DisplayName = $app.DisplayName
                                    Publisher = $app.Publisher
                                    Version = $app.DisplayVersion
                                    UserContext = $userContext
                                    UserSID = $_.Name
                                    UninstallString = $app.UninstallString
                                    InstallLocation = $app.InstallLocation
                                    RegistryPath = $uninstallPath
                                }
                                $FoundInstallations += $installation

                                Write-RMMLog "Found user installation: $($app.DisplayName)" -Level Detect
                                Write-RMMLog "  User: $userContext" -Level Detect
                                Write-RMMLog "  Publisher: $($app.Publisher)" -Level Detect
                                Write-RMMLog "  Version: $($app.DisplayVersion)" -Level Detect
                            }
                        }
                    } catch {
                        Write-RMMLog "Error checking user registry path $uninstallPath`: $($_.Exception.Message)" -Level Warning
                    }
                }
            }
        }

        # Also check for user-level installations in common locations
        $UserPaths = @(
            "$env:LOCALAPPDATA\Programs\Messenger",
            "$env:APPDATA\Messenger"
        )

        foreach ($Path in $UserPaths) {
            if (Test-Path $Path) {
                $UninstallExe = Join-Path $Path "Uninstall Messenger.exe"
                if (Test-Path $UninstallExe) {
                    $installation = [PSCustomObject]@{
                        DisplayName = "Messenger (User Installation)"
                        Publisher = "Unknown"
                        Version = "Unknown"
                        UserContext = "$env:USERDOMAIN\$env:USERNAME"
                        UserSID = "Current"
                        UninstallString = "`"$UninstallExe`" /S"
                        InstallLocation = $Path
                        RegistryPath = "File System"
                    }
                    $FoundInstallations += $installation

                    Write-RMMLog "Found user installation via file system: $Path" -Level Detect
                    Write-RMMLog "  Uninstaller: $UninstallExe" -Level Detect
                }
            }
        }
    } catch {
        Write-RMMLog "Error during user context detection: $($_.Exception.Message)" -Level Warning
    }

    if ($FoundInstallations.Count -eq 0) {
        Write-RMMLog "No user context Messenger installations found" -Level Detect
    }

    return $FoundInstallations
}

function Uninstall-SystemContextMessenger {
    <#
    .SYNOPSIS
    Uninstalls Messenger from system context using MSI product codes
    #>
    param([array]$Installations)

    if ($Installations.Count -eq 0) {
        Write-RMMLog "No system context installations to uninstall" -Level Status
        return $true
    }

    Write-RMMLog "Uninstalling $($Installations.Count) system context installation(s)..." -Level Status

    $UninstallSuccess = $true

    foreach ($installation in $Installations) {
        Write-RMMLog "Uninstalling: $($installation.DisplayName)" -Level Status
        Write-RMMLog "Product Code: $($installation.ProductCode)" -Level Config

        try {
            # Use msiexec to uninstall using product code
            $Arguments = "/x `"$($installation.ProductCode)`" /qn /norestart"
            Write-RMMLog "Executing: msiexec.exe $Arguments" -Level Config

            $process = Start-Process -FilePath "msiexec.exe" -ArgumentList $Arguments -Wait -PassThru -NoNewWindow
            $exitCode = $process.ExitCode

            Write-RMMLog "MSI uninstall completed with exit code: $exitCode" -Level Status

            switch ($exitCode) {
                0 {
                    Write-RMMLog "Successfully uninstalled: $($installation.DisplayName)" -Level Success
                }
                3010 {
                    Write-RMMLog "Successfully uninstalled: $($installation.DisplayName) (reboot required)" -Level Success
                }
                1605 {
                    Write-RMMLog "Product not found (may have been already uninstalled): $($installation.DisplayName)" -Level Warning
                }
                default {
                    Write-RMMLog "Failed to uninstall $($installation.DisplayName) - Exit code: $exitCode" -Level Error
                    $UninstallSuccess = $false
                }
            }
        } catch {
            Write-RMMLog "Error uninstalling $($installation.DisplayName): $($_.Exception.Message)" -Level Error
            $UninstallSuccess = $false
        }
    }

    return $UninstallSuccess
}

function Uninstall-UserContextMessenger {
    <#
    .SYNOPSIS
    Uninstalls Messenger from user context using uninstall strings or executables
    #>
    param([array]$Installations)

    if ($Installations.Count -eq 0) {
        Write-RMMLog "No user context installations to uninstall" -Level Status
        return $true
    }

    Write-RMMLog "Uninstalling $($Installations.Count) user context installation(s)..." -Level Status

    $UninstallSuccess = $true

    foreach ($installation in $Installations) {
        Write-RMMLog "Uninstalling: $($installation.DisplayName)" -Level Status
        Write-RMMLog "User: $($installation.UserContext)" -Level Config

        try {
            if ($installation.UninstallString) {
                # Use the uninstall string from registry or file system detection
                $UninstallCommand = $installation.UninstallString

                # Handle different uninstall string formats
                if ($UninstallCommand -like "*Uninstall Messenger.exe*") {
                    # User-level uninstaller executable
                    if ($UninstallCommand -notlike "*/S*") {
                        $UninstallCommand = $UninstallCommand.TrimEnd('"') + " /S" + '"'
                    }
                    Write-RMMLog "Executing user uninstaller: $UninstallCommand" -Level Config

                    # Extract executable path and arguments
                    if ($UninstallCommand -match '^"([^"]+)"(.*)$') {
                        $ExePath = $matches[1]
                        $Arguments = $matches[2].Trim()
                    } else {
                        $ExePath = $UninstallCommand.Split(' ')[0]
                        $Arguments = ($UninstallCommand.Split(' ')[1..999] -join ' ')
                    }

                    if (Test-Path $ExePath) {
                        $process = Start-Process -FilePath $ExePath -ArgumentList $Arguments -Wait -PassThru -NoNewWindow
                        $exitCode = $process.ExitCode

                        if ($exitCode -eq 0) {
                            Write-RMMLog "Successfully uninstalled: $($installation.DisplayName)" -Level Success
                        } else {
                            Write-RMMLog "Uninstall completed with exit code: $exitCode for $($installation.DisplayName)" -Level Warning
                        }
                    } else {
                        Write-RMMLog "Uninstaller not found: $ExePath" -Level Error
                        $UninstallSuccess = $false
                    }
                } else {
                    # Other uninstall string formats (MSI, etc.)
                    Write-RMMLog "Executing uninstall command: $UninstallCommand" -Level Config
                    $process = Start-Process -FilePath "cmd.exe" -ArgumentList "/c", $UninstallCommand -Wait -PassThru -NoNewWindow
                    $exitCode = $process.ExitCode

                    if ($exitCode -eq 0) {
                        Write-RMMLog "Successfully uninstalled: $($installation.DisplayName)" -Level Success
                    } else {
                        Write-RMMLog "Uninstall completed with exit code: $exitCode for $($installation.DisplayName)" -Level Warning
                    }
                }
            } else {
                Write-RMMLog "No uninstall string available for: $($installation.DisplayName)" -Level Warning
                $UninstallSuccess = $false
            }
        } catch {
            Write-RMMLog "Error uninstalling $($installation.DisplayName): $($_.Exception.Message)" -Level Error
            $UninstallSuccess = $false
        }
    }

    return $UninstallSuccess
}

function Test-MessengerRemoved {
    <#
    .SYNOPSIS
    Verifies that Messenger has been successfully uninstalled
    #>

    Write-RMMLog "Verifying Messenger removal..." -Level Status

    $SystemInstallations = Get-SystemContextMessenger
    $UserInstallations = Get-UserContextMessenger

    $TotalRemaining = $SystemInstallations.Count + $UserInstallations.Count

    if ($TotalRemaining -eq 0) {
        Write-RMMLog "Verification successful: No Messenger installations detected" -Level Success
        return $true
    } else {
        Write-RMMLog "Verification failed: $TotalRemaining Messenger installation(s) still detected" -Level Error

        if ($SystemInstallations.Count -gt 0) {
            Write-RMMLog "Remaining system installations: $($SystemInstallations.Count)" -Level Error
            foreach ($installation in $SystemInstallations) {
                Write-RMMLog "  - $($installation.DisplayName)" -Level Error
            }
        }

        if ($UserInstallations.Count -gt 0) {
            Write-RMMLog "Remaining user installations: $($UserInstallations.Count)" -Level Error
            foreach ($installation in $UserInstallations) {
                Write-RMMLog "  - $($installation.DisplayName) ($($installation.UserContext))" -Level Error
            }
        }

        return $false
    }
}

# ============================================
# MAIN EXECUTION
# ============================================

try {
    Write-RMMLog "Starting Messenger uninstall process..." -Level Status
    Write-RMMLog ""

    # Step 1: Terminate running processes
    Stop-MessengerProcesses -ForceKill $ForceKill
    Write-RMMLog ""

    # Step 2: Detect installations
    $SystemInstallations = @()
    $UserInstallations = @()

    if (-not $SkipSystemContext) {
        $SystemInstallations = Get-SystemContextMessenger
    } else {
        Write-RMMLog "Skipping system context detection (SkipSystemContext = true)" -Level Config
    }

    if (-not $SkipUserContext) {
        $UserInstallations = Get-UserContextMessenger
    } else {
        Write-RMMLog "Skipping user context detection (SkipUserContext = true)" -Level Config
    }

    Write-RMMLog ""

    # Check if any installations were found
    $TotalInstallations = $SystemInstallations.Count + $UserInstallations.Count

    if ($TotalInstallations -eq 0) {
        Write-RMMLog "No Messenger installations detected on this system" -Level Success
        Write-RMMLog "Uninstall process completed successfully" -Level Success
        Stop-Transcript
        exit 0
    }

    Write-RMMLog "Found $TotalInstallations Messenger installation(s) to remove:" -Level Status
    Write-RMMLog "- System context: $($SystemInstallations.Count)" -Level Status
    Write-RMMLog "- User context: $($UserInstallations.Count)" -Level Status
    Write-RMMLog ""

    # Step 3: Uninstall system context installations
    $SystemSuccess = $true
    if ($SystemInstallations.Count -gt 0 -and -not $SkipSystemContext) {
        $SystemSuccess = Uninstall-SystemContextMessenger -Installations $SystemInstallations
        Write-RMMLog ""
    }

    # Step 4: Uninstall user context installations
    $UserSuccess = $true
    if ($UserInstallations.Count -gt 0 -and -not $SkipUserContext) {
        $UserSuccess = Uninstall-UserContextMessenger -Installations $UserInstallations
        Write-RMMLog ""
    }

    # Step 5: Verify uninstall if requested
    $VerificationSuccess = $true
    if ($VerifyUninstall) {
        $VerificationSuccess = Test-MessengerRemoved
        Write-RMMLog ""
    }

    # Step 6: Determine final result
    $OverallSuccess = $SystemSuccess -and $UserSuccess -and $VerificationSuccess

    if ($OverallSuccess) {
        Write-RMMLog "Messenger uninstall completed successfully" -Level Success
        $ExitCode = 0
    } elseif ($SystemSuccess -or $UserSuccess) {
        Write-RMMLog "Messenger uninstall partially successful" -Level Warning
        Write-RMMLog "Some installations were removed but others may have failed" -Level Warning
        $ExitCode = 2
    } else {
        Write-RMMLog "Messenger uninstall failed" -Level Error
        $ExitCode = 1
    }

    Write-RMMLog ""
    Write-RMMLog "=============================================="
    Write-RMMLog "Messenger Uninstall Summary:" -Level Status
    Write-RMMLog "- System context success: $SystemSuccess" -Level Status
    Write-RMMLog "- User context success: $UserSuccess" -Level Status
    Write-RMMLog "- Verification success: $VerificationSuccess" -Level Status
    Write-RMMLog "- Overall result: $(if ($OverallSuccess) { 'SUCCESS' } elseif ($ExitCode -eq 2) { 'PARTIAL SUCCESS' } else { 'FAILED' })" -Level Status
    Write-RMMLog "- Exit code: $ExitCode" -Level Status
    Write-RMMLog "- End time: $(Get-Date)" -Level Status
    Write-RMMLog "=============================================="

} catch {
    Write-RMMLog "Critical error during Messenger uninstall: $($_.Exception.Message)" -Level Error
    Write-RMMLog "Stack trace: $($_.ScriptStackTrace)" -Level Error
    $ExitCode = 1
} finally {
    Stop-Transcript
    exit $ExitCode
}
