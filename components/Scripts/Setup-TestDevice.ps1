# Setup Test Device Environment
# This script prepares a Windows VM for automated testing of Datto RMM components
# GitHub Actions Pipeline Test - Updated 2025-01-14

<#
.SYNOPSIS
    Sets up a Windows device for automated testing of Datto RMM components
.DESCRIPTION
    Creates the necessary directory structure, logging configuration, and test environment
    for automated component testing within a Datto RMM environment.
.PARAMETER TestResultsPath
    Path where test results and logs will be stored
.PARAMETER CleanupOldResults
    Remove test results older than specified days
.EXAMPLE
    .\Setup-TestDevice.ps1 -TestResultsPath "C:\TestResults" -CleanupOldResults 7
.NOTES
    Author: Datto RMM Automation Team
    Version: 1.0
    Created: 2025-07-14
    
    This script should be deployed as a Datto RMM Scripts component to prepare
    the test device environment.
#>

param(
    [Parameter(Mandatory = $false)]
    [string]$TestResultsPath = "C:\TestResults",
    
    [Parameter(Mandatory = $false)]
    [int]$CleanupOldResults = 7,
    
    [Parameter(Mandatory = $false)]
    [switch]$Force
)

# Initialize logging
$LogFile = "$env:TEMP\Setup-TestDevice-$(Get-Date -Format 'yyyyMMdd-HHmmss').log"
Start-Transcript -Path $LogFile -Append

try {
    Write-Output "=== Datto RMM Test Device Setup ==="
    Write-Output "Timestamp: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
    Write-Output "Computer: $env:COMPUTERNAME"
    Write-Output "User: $env:USERNAME"
    Write-Output "Test Results Path: $TestResultsPath"
    
    # Create test results directory structure
    Write-Output "`n--- Creating Directory Structure ---"
    $directories = @(
        $TestResultsPath,
        "$TestResultsPath\Logs",
        "$TestResultsPath\Components",
        "$TestResultsPath\Reports",
        "$TestResultsPath\Archive"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -Path $dir -ItemType Directory -Force | Out-Null
            Write-Output "✓ Created: $dir"
        } else {
            Write-Output "✓ Exists: $dir"
        }
    }
    
    # Set up PowerShell execution policy for testing
    Write-Output "`n--- Configuring PowerShell Environment ---"
    try {
        $currentPolicy = Get-ExecutionPolicy -Scope LocalMachine
        Write-Output "Current execution policy: $currentPolicy"
        
        if ($currentPolicy -eq "Restricted") {
            Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope LocalMachine -Force
            Write-Output "✓ Updated execution policy to RemoteSigned"
        } else {
            Write-Output "✓ Execution policy is already permissive: $currentPolicy"
        }
    } catch {
        Write-Warning "Could not modify execution policy: $($_.Exception.Message)"
    }
    
    # Create test configuration file
    Write-Output "`n--- Creating Test Configuration ---"
    $testConfig = @{
        TestDeviceId = $env:COMPUTERNAME
        TestResultsPath = $TestResultsPath
        SetupDate = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
        PowerShellVersion = $PSVersionTable.PSVersion.ToString()
        OSVersion = [System.Environment]::OSVersion.VersionString
        DotNetVersion = [System.Environment]::Version.ToString()
        MaxLogRetentionDays = $CleanupOldResults
        TestComponentPrefix = "TEST-"
    }
    
    $configPath = "$TestResultsPath\test-config.json"
    $testConfig | ConvertTo-Json -Depth 3 | Out-File -FilePath $configPath -Encoding UTF8
    Write-Output "✓ Created test configuration: $configPath"
    
    # Create test helper functions script
    Write-Output "`n--- Creating Test Helper Functions ---"
    $helperScript = @'
# Test Helper Functions for Datto RMM Components
# Auto-generated by Setup-TestDevice.ps1

function Write-TestLog {
    param(
        [string]$Message,
        [string]$Level = "INFO",
        [string]$Component = "TEST"
    )
    
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    $logEntry = "[$timestamp] [$Level] [$Component] $Message"
    
    # Write to console
    switch ($Level) {
        "ERROR" { Write-Error $logEntry }
        "WARNING" { Write-Warning $logEntry }
        default { Write-Output $logEntry }
    }
    
    # Write to log file
    $logFile = "C:\TestResults\Logs\test-$(Get-Date -Format 'yyyyMMdd').log"
    $logEntry | Out-File -FilePath $logFile -Append -Encoding UTF8
}

function Start-TestComponent {
    param(
        [string]$ComponentName,
        [hashtable]$Parameters = @{}
    )
    
    $testId = [System.Guid]::NewGuid().ToString("N").Substring(0, 8)
    $startTime = Get-Date
    
    Write-TestLog "Starting test component: $ComponentName (ID: $testId)" "INFO" $ComponentName
    
    return @{
        TestId = $testId
        ComponentName = $ComponentName
        StartTime = $startTime
        Parameters = $Parameters
    }
}

function Stop-TestComponent {
    param(
        [hashtable]$TestContext,
        [string]$Result = "SUCCESS",
        [string]$Message = ""
    )
    
    $endTime = Get-Date
    $duration = $endTime - $TestContext.StartTime
    
    $testResult = @{
        TestId = $TestContext.TestId
        ComponentName = $TestContext.ComponentName
        StartTime = $TestContext.StartTime
        EndTime = $endTime
        Duration = $duration.TotalSeconds
        Result = $Result
        Message = $Message
        Parameters = $TestContext.Parameters
    }
    
    # Log result
    Write-TestLog "Test completed: $($TestContext.ComponentName) - $Result ($($duration.TotalSeconds)s)" "INFO" $TestContext.ComponentName
    
    # Save detailed result
    $resultFile = "C:\TestResults\Reports\test-$($TestContext.TestId).json"
    $testResult | ConvertTo-Json -Depth 3 | Out-File -FilePath $resultFile -Encoding UTF8
    
    return $testResult
}

function Get-TestEnvironmentInfo {
    return @{
        ComputerName = $env:COMPUTERNAME
        OSVersion = [System.Environment]::OSVersion.VersionString
        PowerShellVersion = $PSVersionTable.PSVersion.ToString()
        DotNetVersion = [System.Environment]::Version.ToString()
        CurrentUser = $env:USERNAME
        TestResultsPath = "C:\TestResults"
        Timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    }
}

# Export functions for use in test components
Export-ModuleMember -Function Write-TestLog, Start-TestComponent, Stop-TestComponent, Get-TestEnvironmentInfo
'@
    
    $helperPath = "$TestResultsPath\TestHelpers.psm1"
    $helperScript | Out-File -FilePath $helperPath -Encoding UTF8
    Write-Output "✓ Created test helper module: $helperPath"
    
    # Clean up old test results if requested
    if ($CleanupOldResults -gt 0) {
        Write-Output "`n--- Cleaning Up Old Test Results ---"
        $cutoffDate = (Get-Date).AddDays(-$CleanupOldResults)
        
        $oldFiles = Get-ChildItem -Path "$TestResultsPath\Logs", "$TestResultsPath\Reports" -Recurse -File | 
                   Where-Object { $_.LastWriteTime -lt $cutoffDate }
        
        if ($oldFiles) {
            $oldFiles | ForEach-Object {
                Remove-Item $_.FullName -Force
                Write-Output "✓ Removed old file: $($_.Name)"
            }
            Write-Output "✓ Cleaned up $($oldFiles.Count) old files"
        } else {
            Write-Output "✓ No old files to clean up"
        }
    }
    
    # Test the environment
    Write-Output "`n--- Testing Environment ---"
    
    # Test PowerShell execution
    $testScript = "Write-Output 'Test execution successful'; exit 0"
    $testResult = Invoke-Expression $testScript
    Write-Output "✓ PowerShell execution test: $testResult"
    
    # Test file operations
    $testFile = "$TestResultsPath\test-write.tmp"
    "Test content" | Out-File -FilePath $testFile -Encoding UTF8
    if (Test-Path $testFile) {
        Remove-Item $testFile -Force
        Write-Output "✓ File operations test: SUCCESS"
    } else {
        Write-Warning "File operations test: FAILED"
    }
    
    # Create summary report
    Write-Output "`n--- Setup Summary ---"
    $summary = @{
        SetupDate = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
        ComputerName = $env:COMPUTERNAME
        TestResultsPath = $TestResultsPath
        DirectoriesCreated = $directories.Count
        ConfigurationFile = $configPath
        HelperModule = $helperPath
        CleanupDays = $CleanupOldResults
        Status = "SUCCESS"
    }
    
    $summaryPath = "$TestResultsPath\setup-summary.json"
    $summary | ConvertTo-Json -Depth 3 | Out-File -FilePath $summaryPath -Encoding UTF8
    
    Write-Output "✓ Test device setup completed successfully!"
    Write-Output "✓ Summary saved to: $summaryPath"
    Write-Output "✓ Test environment ready for automated component testing"
    
    # Exit with success
    exit 0
    
} catch {
    Write-Error "Setup failed: $($_.Exception.Message)"
    Write-Error "Stack trace: $($_.ScriptStackTrace)"
    exit 1
    
} finally {
    Stop-Transcript
    Write-Output "Setup log saved to: $LogFile"
}
