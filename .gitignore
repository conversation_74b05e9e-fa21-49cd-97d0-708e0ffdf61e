# ===========================================
# Development and Build Files
# ===========================================

# Test results and logs
test-results/
TestResults/
logs/
*.test.log

# Temporary files
temp/
tmp/
*.tmp
*.temp

# ===========================================
# Standard Development Files
# ===========================================

# macOS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes

# Windows specific
ehthumbs.db
Thumbs.db

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# PowerShell specific
*.ps1xml
PSReadLineHistory.txt

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Dependency directories
node_modules/
bower_components/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Build outputs
dist/
build/
out/

# Cache directories
.cache/
.parcel-cache/

# Serverless directories
.serverless/
.DS_Store
